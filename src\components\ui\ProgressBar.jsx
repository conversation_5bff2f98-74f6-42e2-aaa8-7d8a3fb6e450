import { toPersianNumber } from "utils/helper";
import ToolTip from "components/ui/ToolTip";

const ProgressBar = ({ data }) => {
  if (data?.length == 0) {
    return (
      <p className="py-1.5 text-center font-body-bold-large text-light-neutral-text-medium">
        موردی یافت نشد
      </p>
    );
  }
  return (
    <>
      <div className="flex flex-col items-center w-full">
        {/* Percentage Labels */}
        <div className="flex justify-between w-full font-semibold mb-1">
          {data?.map((item) => (
            <span
              key={item?.id}
              className="font-body-small cursor-pointer"
              style={{ color: item?.color }}
            >
              <ToolTip comp={toPersianNumber(item?.tooltip)}>
                {toPersianNumber(item?.percentage)}%
              </ToolTip>
            </span>
          ))}
        </div>
        {/* Progress Bar */}
        <div className="w-full h-3 rounded-full bg-gray-200 flex overflow-hidden">
          {data?.map((item, index) => (
            <div
              key={item?.id}
              className={`h-full first:rounded-l-full last:rounded-r-full`}
              style={{
                width: `${item?.percentage}%`,
                backgroundColor: item?.color,
              }}
            >
              {/* <ToolTip comp={item?.title} /> */}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default ProgressBar;
