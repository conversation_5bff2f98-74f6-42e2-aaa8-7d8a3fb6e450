import { useEffect, useState } from "react";
import TimeLine from "components/Charts/TimeLine";
import search from "service/api/search";
import PropTypes from "prop-types";
import CardLoading from "components/ui/CardLoading";

const WaveTimeline = ({ id }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTimelineData = async () => {
      if (!id) return;

      setLoading(true);
      setError(null);

      try {
        const response = await search.getTrendsPeak(id);
        console.log(response);
        if (response?.data?.status === "OK" && response?.data?.data) {
          setData(response.data.data);
        } else {
          setError("خطا در بارگذاری داده!");
        }
      } catch (err) {
        console.error("Error fetching timeline data:", err);
        setError("خطا در بارگذاری داده!");
      } finally {
        setLoading(false);
      }
    };

    fetchTimelineData();
  }, [id]);

  return (
    <div
      className="bg-light-neutral-surface-card rounded-lg p-3"
      style={{
        boxShadow: "0px 2px 20px 0px #0000000D",
      }}
    >
      <p className="font-subtitle-large mb-7">روند زمانی اوج‌گیری موج</p>

      {loading && (
        <div className="flex justify-center items-center h-64">
          <CardLoading />
        </div>
      )}

      {error && (
        <div className="flex justify-center items-center h-64">
          <div className="text-black font-subtitle-medium">{error}</div>
        </div>
      )}

      {!loading && !error && <TimeLine data={data} />}
    </div>
  );
};

WaveTimeline.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default WaveTimeline;
